"""
Script main để chạy toàn bộ pipeline: PDF -> MD -> DOCX -> Merged DOCX
Sử dụng Paddle PP-StructureV3 để OCR PDF thành Markdown, 
sau đ<PERSON> convert thành DOCX và merge lại thành 1 file
"""

import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import các module đã tạo
from pdf_to_multiple_md import PDFToMultipleMarkdown
from md_to_docx import MarkdownToDocxConverter
from merge_docx import DocxMerger


class PDFToDocxPipeline:
    def __init__(self, output_base_dir: str = "./pipeline_output"):
        """
        Khởi tạo pipeline
        
        Args:
            output_base_dir: Thư mục gốc để lưu tất cả output
        """
        self.output_base_dir = Path(output_base_dir)
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        
        # Khởi tạo các converter
        self.pdf_to_md = PDFToMultipleMarkdown(str(self.output_base_dir / "markdown"))
        self.md_to_docx = MarkdownToDocxConverter()
        self.docx_merger = DocxMerger()
        
        print(f"📁 Pipeline output directory: {self.output_base_dir}")
    
    def process_single_pdf(self, pdf_path: str, final_output_name: str = None) -> Dict[str, Any]:
        """
        Xử lý 1 file PDF qua toàn bộ pipeline
        
        Args:
            pdf_path: Đường dẫn đến file PDF
            final_output_name: Tên file DOCX cuối cùng (optional)
            
        Returns:
            Dict chứa thông tin về quá trình xử lý
        """
        pdf_file = Path(pdf_path)
        if not pdf_file.exists():
            raise FileNotFoundError(f"File PDF không tồn tại: {pdf_path}")
        
        pdf_name = pdf_file.stem
        start_time = time.time()
        
        print(f"\n{'='*60}")
        print(f"🚀 BẮT ĐẦU PIPELINE CHO: {pdf_file.name}")
        print(f"{'='*60}")
        
        result = {
            "pdf_file": str(pdf_file),
            "pdf_name": pdf_name,
            "start_time": start_time,
            "steps": {}
        }
        
        try:
            # BƯỚC 1: PDF -> Multiple MD files
            print(f"\n📄 BƯỚC 1: Convert PDF thành Markdown files...")
            step1_start = time.time()
            
            md_metadata = self.pdf_to_md.convert_pdf_to_multiple_md(str(pdf_file))
            self.pdf_to_md.create_index_file(md_metadata)
            
            step1_time = time.time() - step1_start
            result["steps"]["pdf_to_md"] = {
                "success": True,
                "duration": step1_time,
                "output_dir": md_metadata["output_directory"],
                "md_files_count": len(md_metadata["markdown_files"]),
                "image_files_count": len(md_metadata["image_files"])
            }
            
            print(f"✅ BƯỚC 1 hoàn thành ({step1_time:.2f}s)")
            print(f"   - Tạo được {len(md_metadata['markdown_files'])} file MD")
            print(f"   - Tạo được {len(md_metadata['image_files'])} file hình ảnh")
            
            # BƯỚC 2: MD files -> DOCX files
            print(f"\n📝 BƯỚC 2: Convert Markdown files thành DOCX files...")
            step2_start = time.time()
            
            md_dir = md_metadata["output_directory"]
            docx_metadata = self.md_to_docx.convert_multiple_md_to_docx(md_dir)
            
            step2_time = time.time() - step2_start
            result["steps"]["md_to_docx"] = {
                "success": docx_metadata["successful_conversions"] > 0,
                "duration": step2_time,
                "output_dir": docx_metadata["output_directory"],
                "successful_conversions": docx_metadata["successful_conversions"],
                "failed_conversions": docx_metadata["failed_conversions"]
            }
            
            print(f"✅ BƯỚC 2 hoàn thành ({step2_time:.2f}s)")
            print(f"   - Thành công: {docx_metadata['successful_conversions']}")
            print(f"   - Thất bại: {docx_metadata['failed_conversions']}")
            
            # BƯỚC 3: Merge DOCX files thành 1 file
            print(f"\n📚 BƯỚC 3: Merge tất cả DOCX files thành 1 file...")
            step3_start = time.time()
            
            docx_dir = docx_metadata["output_directory"]
            
            # Tạo tên file output cuối cùng
            if final_output_name is None:
                final_output_name = f"{pdf_name}_merged.docx"
            elif not final_output_name.endswith('.docx'):
                final_output_name += '.docx'
            
            final_output_path = self.output_base_dir / "final" / final_output_name
            
            merge_metadata = self.docx_merger.merge_from_directory(
                docx_dir, str(final_output_path)
            )
            
            step3_time = time.time() - step3_start
            result["steps"]["merge_docx"] = {
                "success": merge_metadata["success"],
                "duration": step3_time,
                "output_file": str(final_output_path),
                "merged_files_count": merge_metadata["total_files"],
                "output_size": merge_metadata["output_size"]
            }
            
            print(f"✅ BƯỚC 3 hoàn thành ({step3_time:.2f}s)")
            print(f"   - Merged {merge_metadata['total_files']} files")
            print(f"   - Output: {final_output_path}")
            print(f"   - Size: {merge_metadata['output_size']} bytes")
            
            # Tính tổng thời gian
            total_time = time.time() - start_time
            result["total_duration"] = total_time
            result["success"] = True
            result["final_output"] = str(final_output_path)
            
            print(f"\n🎉 PIPELINE HOÀN THÀNH!")
            print(f"⏱️  Tổng thời gian: {total_time:.2f}s")
            print(f"📄 File cuối cùng: {final_output_path}")
            
        except Exception as e:
            result["success"] = False
            result["error"] = str(e)
            result["total_duration"] = time.time() - start_time
            
            print(f"\n❌ PIPELINE THẤT BẠI: {e}")
        
        return result
    
    def process_multiple_pdfs(self, pdf_files: List[str]) -> Dict[str, Any]:
        """
        Xử lý nhiều file PDF
        
        Args:
            pdf_files: Danh sách đường dẫn các file PDF
            
        Returns:
            Dict chứa thông tin về quá trình xử lý tất cả files
        """
        start_time = time.time()
        results = []
        successful_count = 0
        
        print(f"\n🚀 BẮT ĐẦU PIPELINE CHO {len(pdf_files)} FILE PDF")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n{'='*60}")
            print(f"📄 XỬ LÝ FILE {i}/{len(pdf_files)}: {Path(pdf_file).name}")
            print(f"{'='*60}")
            
            try:
                result = self.process_single_pdf(pdf_file)
                results.append(result)
                
                if result["success"]:
                    successful_count += 1
                    
            except Exception as e:
                print(f"❌ Lỗi khi xử lý {pdf_file}: {e}")
                results.append({
                    "pdf_file": pdf_file,
                    "success": False,
                    "error": str(e)
                })
        
        # Tạo metadata tổng
        total_metadata = {
            "total_files": len(pdf_files),
            "successful_files": successful_count,
            "failed_files": len(pdf_files) - successful_count,
            "total_duration": time.time() - start_time,
            "results": results
        }
        
        # Lưu metadata
        metadata_file = self.output_base_dir / "pipeline_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(total_metadata, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 TỔNG KẾT PIPELINE:")
        print(f"- Tổng số file: {len(pdf_files)}")
        print(f"- Thành công: {successful_count}")
        print(f"- Thất bại: {len(pdf_files) - successful_count}")
        print(f"- Tổng thời gian: {total_metadata['total_duration']:.2f}s")
        print(f"- Metadata: {metadata_file}")
        
        return total_metadata


def main():
    """Hàm main với command line interface"""
    parser = argparse.ArgumentParser(
        description="Pipeline convert PDF thành DOCX sử dụng PP-StructureV3"
    )
    parser.add_argument("pdf_files", nargs="+", help="Đường dẫn đến file(s) PDF")
    parser.add_argument("-o", "--output", default="./pipeline_output", 
                       help="Thư mục output (mặc định: ./pipeline_output)")
    parser.add_argument("--final-name", help="Tên file DOCX cuối cùng (chỉ dùng khi xử lý 1 file)")
    
    args = parser.parse_args()
    
    # Khởi tạo pipeline
    pipeline = PDFToDocxPipeline(args.output)
    
    try:
        if len(args.pdf_files) == 1:
            # Xử lý 1 file
            result = pipeline.process_single_pdf(args.pdf_files[0], args.final_name)
            if result["success"]:
                print(f"\n✅ Thành công! File output: {result['final_output']}")
            else:
                print(f"\n❌ Thất bại: {result.get('error', 'Unknown error')}")
        else:
            # Xử lý nhiều file
            result = pipeline.process_multiple_pdfs(args.pdf_files)
            print(f"\n✅ Hoàn thành xử lý {result['successful_files']}/{result['total_files']} files")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")


if __name__ == "__main__":
    # Nếu chạy trực tiếp không có args, test với file mẫu
    import sys
    if len(sys.argv) == 1:
        print("🧪 CHẠY TEST VỚI FILE MẪU")
        pipeline = PDFToDocxPipeline("./test_pipeline_output")
        
        # Test với file có sẵn
        test_files = ["sample.pdf", "test_file.pdf"]
        existing_files = [f for f in test_files if Path(f).exists()]
        
        if existing_files:
            pipeline.process_multiple_pdfs(existing_files)
        else:
            print("⚠️  Không tìm thấy file PDF nào để test")
    else:
        main()
