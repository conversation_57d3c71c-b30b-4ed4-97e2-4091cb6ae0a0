"""
Script để merge tất cả các file DOCX thành 1 file DOCX duy nhất
"""

import json
from pathlib import Path
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
import re


class DocxMerger:
    def __init__(self):
        """Khởi tạo merger"""
        pass
    
    def add_page_break(self, doc: Document):
        """Thêm page break vào document"""
        paragraph = doc.add_paragraph()
        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
        fldChar = OxmlElement('w:br')
        fldChar.set(qn('w:type'), 'page')
        run._element.append(fldChar)
    
    def merge_docx_files(self, docx_files: List[str], output_file: str, 
                        add_page_breaks: bool = True, add_headers: bool = True) -> bool:
        """
        Merge nhiều file DOCX thành 1 file
        
        Args:
            docx_files: Danh sách đường dẫn các file DOCX
            output_file: Đ<PERSON>ờng dẫn file output
            add_page_breaks: Có thêm page break giữa các file không
            add_headers: Có thêm header cho mỗi file không
            
        Returns:
            True nếu thành công
        """
        try:
            # Tạo document mới
            merged_doc = Document()
            
            # Thêm title page
            title_paragraph = merged_doc.add_heading('Tài liệu được tổng hợp', 0)
            title_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            
            merged_doc.add_paragraph()
            merged_doc.add_paragraph(f"Tài liệu này được tạo bằng cách tổng hợp {len(docx_files)} file DOCX:")
            
            # Liệt kê các file
            for i, docx_file in enumerate(docx_files, 1):
                file_name = Path(docx_file).name
                merged_doc.add_paragraph(f"{i}. {file_name}", style='List Number')
            
            merged_doc.add_paragraph()
            
            # Thêm page break sau title page
            if add_page_breaks:
                self.add_page_break(merged_doc)
            
            # Merge từng file
            for i, docx_file in enumerate(docx_files):
                file_path = Path(docx_file)
                
                if not file_path.exists():
                    print(f"⚠️  File không tồn tại: {docx_file}")
                    continue
                
                print(f"Đang merge: {file_path.name}")
                
                try:
                    # Mở file DOCX
                    source_doc = Document(str(file_path))
                    
                    # Thêm header cho file này
                    if add_headers:
                        header_text = f"--- {file_path.stem} ---"
                        header_paragraph = merged_doc.add_heading(header_text, level=1)
                        header_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                        merged_doc.add_paragraph()
                    
                    # Copy tất cả paragraphs từ source document
                    for paragraph in source_doc.paragraphs:
                        # Tạo paragraph mới trong merged document
                        new_paragraph = merged_doc.add_paragraph()
                        
                        # Copy style nếu có
                        if paragraph.style:
                            try:
                                new_paragraph.style = paragraph.style
                            except:
                                # Nếu style không tồn tại, sử dụng style mặc định
                                pass
                        
                        # Copy text và formatting
                        for run in paragraph.runs:
                            new_run = new_paragraph.add_run(run.text)
                            
                            # Copy formatting
                            if run.bold:
                                new_run.bold = True
                            if run.italic:
                                new_run.italic = True
                            if run.underline:
                                new_run.underline = True
                            if run.font.size:
                                new_run.font.size = run.font.size
                            if run.font.name:
                                new_run.font.name = run.font.name
                    
                    # Copy tables nếu có
                    for table in source_doc.tables:
                        # Tạo table mới
                        new_table = merged_doc.add_table(rows=len(table.rows), 
                                                       cols=len(table.columns))
                        
                        # Copy nội dung table
                        for i, row in enumerate(table.rows):
                            for j, cell in enumerate(row.cells):
                                new_table.cell(i, j).text = cell.text
                    
                    # Thêm page break giữa các file (trừ file cuối)
                    if add_page_breaks and i < len(docx_files) - 1:
                        merged_doc.add_paragraph()
                        self.add_page_break(merged_doc)
                    
                except Exception as e:
                    print(f"❌ Lỗi khi merge {file_path.name}: {e}")
                    continue
            
            # Lưu file merged
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            merged_doc.save(str(output_path))
            
            print(f"✅ Đã merge thành công vào: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi merge documents: {e}")
            return False
    
    def merge_from_directory(self, input_dir: str, output_file: str = None, 
                           pattern: str = "*.docx") -> Dict[str, Any]:
        """
        Merge tất cả file DOCX trong thư mục
        
        Args:
            input_dir: Thư mục chứa các file DOCX
            output_file: File output (mặc định là merged.docx trong input_dir)
            pattern: Pattern để tìm file (mặc định *.docx)
            
        Returns:
            Dict chứa thông tin về quá trình merge
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"Thư mục không tồn tại: {input_dir}")
        
        # Tìm tất cả file DOCX
        docx_files = list(input_path.glob(pattern))
        
        if not docx_files:
            return {
                "success": False,
                "message": f"Không tìm thấy file nào với pattern {pattern} trong {input_dir}"
            }
        
        # Sắp xếp file theo tên (để đảm bảo thứ tự)
        docx_files.sort(key=lambda x: x.name)
        
        # Tạo output file path
        if output_file is None:
            output_file = input_path / "merged_document.docx"
        else:
            output_file = Path(output_file)
        
        print(f"Tìm thấy {len(docx_files)} file DOCX để merge:")
        for docx_file in docx_files:
            print(f"  - {docx_file.name}")
        
        # Merge files
        success = self.merge_docx_files([str(f) for f in docx_files], str(output_file))
        
        # Tạo metadata
        metadata = {
            "input_directory": str(input_path),
            "output_file": str(output_file),
            "total_files": len(docx_files),
            "merged_files": [str(f) for f in docx_files],
            "success": success,
            "output_size": output_file.stat().st_size if success and output_file.exists() else 0
        }
        
        # Lưu metadata
        metadata_file = input_path / "merge_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"📊 Metadata lưu tại: {metadata_file}")
        
        return metadata
    
    def merge_multiple_directories(self, base_dir: str, output_dir: str = None) -> Dict[str, Any]:
        """
        Merge DOCX files từ nhiều thư mục con
        
        Args:
            base_dir: Thư mục gốc chứa các thư mục con
            output_dir: Thư mục output
            
        Returns:
            Dict chứa thông tin về quá trình merge
        """
        base_path = Path(base_dir)
        if not base_path.exists():
            raise FileNotFoundError(f"Thư mục không tồn tại: {base_dir}")
        
        if output_dir is None:
            output_path = base_path / "merged_output"
        else:
            output_path = Path(output_dir)
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = []
        
        # Tìm tất cả thư mục con có chứa file DOCX
        for subdir in base_path.iterdir():
            if subdir.is_dir() and subdir.name != "merged_output":
                docx_dir = subdir / "docx"
                if docx_dir.exists():
                    print(f"\n{'='*50}")
                    print(f"Merge DOCX files từ: {docx_dir}")
                    print(f"{'='*50}")
                    
                    output_file = output_path / f"{subdir.name}_merged.docx"
                    
                    try:
                        result = self.merge_from_directory(str(docx_dir), str(output_file))
                        results.append(result)
                    except Exception as e:
                        print(f"❌ Lỗi khi merge {docx_dir}: {e}")
                        results.append({
                            "input_directory": str(docx_dir),
                            "success": False,
                            "error": str(e)
                        })
        
        # Tạo metadata tổng
        total_metadata = {
            "base_directory": str(base_path),
            "output_directory": str(output_path),
            "processed_directories": len(results),
            "successful_merges": sum(1 for r in results if r.get("success", False)),
            "merge_results": results
        }
        
        metadata_file = output_path / "total_merge_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(total_metadata, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 Tổng kết:")
        print(f"- Đã xử lý: {len(results)} thư mục")
        print(f"- Thành công: {total_metadata['successful_merges']}")
        print(f"- Metadata tổng: {metadata_file}")
        
        return total_metadata


def main():
    """Hàm main để test"""
    merger = DocxMerger()
    
    # Test merge từ thư mục output
    base_dir = "./output_md"
    
    if Path(base_dir).exists():
        print(f"Merge tất cả DOCX files từ: {base_dir}")
        try:
            result = merger.merge_multiple_directories(base_dir)
            print("✅ Hoàn thành merge tất cả files")
        except Exception as e:
            print(f"❌ Lỗi: {e}")
    else:
        print(f"⚠️  Thư mục không tồn tại: {base_dir}")


if __name__ == "__main__":
    main()
