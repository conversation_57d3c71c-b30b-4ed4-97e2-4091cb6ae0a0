# PDF to DOCX Pipeline sử dụng Paddle PP-StructureV3

Pipeline tự động để convert file PDF thành nhiều file Markdown, sau đ<PERSON> convert thành DOCX và tổng hợp lại thành 1 file DOCX duy nhất.

## 🚀 Tính năng

- **PDF → Multiple MD**: Sử dụng Paddle PP-StructureV3 để OCR PDF thành nhiều file Markdown (1 file/trang)
- **MD → DOCX**: Convert các file Markdown thành DOCX (hỗ trợ cả pandoc và python-docx)
- **DOCX Merge**: Tổng hợp tất cả file DOCX thành 1 file cuối cùng
- **Batch Processing**: Xử lý nhiều file PDF cùng lúc
- **Metadata Tracking**: Theo dõi chi tiết quá trình xử lý

## 📋 Yêu cầu hệ thống

- Python 3.7+
- CUDA (optional, để tăng tốc OCR)
- Pandoc (optional, đ<PERSON> convert MD → DOCX tốt hơn)

## 🔧 Cài đặt

1. **Clone hoặc download code**

2. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

3. **Cài đặt Pandoc (optional nhưng khuyến nghị):**
   - Windows: Download từ https://pandoc.org/installing.html
   - Ubuntu/Debian: `sudo apt-get install pandoc`
   - macOS: `brew install pandoc`

## 📁 Cấu trúc project

```
ocr_pdf/
├── pdf_to_multiple_md.py      # Convert PDF → Multiple MD
├── md_to_docx.py              # Convert MD → DOCX
├── merge_docx.py              # Merge DOCX files
├── pdf_to_docx_pipeline.py    # Main pipeline script
├── demo.py                    # Demo script
├── requirements.txt           # Dependencies
└── README.md                  # Hướng dẫn này
```

## 🎯 Cách sử dụng

### 1. Demo nhanh

```bash
python demo.py
```

### 2. Sử dụng pipeline chính

**Xử lý 1 file PDF:**
```bash
python pdf_to_docx_pipeline.py your_file.pdf
```

**Xử lý nhiều file PDF:**
```bash
python pdf_to_docx_pipeline.py file1.pdf file2.pdf file3.pdf
```

**Với options:**
```bash
python pdf_to_docx_pipeline.py input.pdf -o ./custom_output --final-name "My_Document"
```

### 3. Sử dụng từng module riêng lẻ

**Chỉ convert PDF → MD:**
```python
from pdf_to_multiple_md import PDFToMultipleMarkdown

converter = PDFToMultipleMarkdown("./output")
metadata = converter.convert_pdf_to_multiple_md("input.pdf")
```

**Chỉ convert MD → DOCX:**
```python
from md_to_docx import MarkdownToDocxConverter

converter = MarkdownToDocxConverter()
metadata = converter.convert_multiple_md_to_docx("./md_folder")
```

**Chỉ merge DOCX:**
```python
from merge_docx import DocxMerger

merger = DocxMerger()
metadata = merger.merge_from_directory("./docx_folder", "merged.docx")
```

## 📊 Output Structure

```
pipeline_output/
├── markdown/
│   └── your_file/
│       ├── page_001.md
│       ├── page_002.md
│       ├── ...
│       ├── images/
│       ├── index.md
│       └── metadata.json
├── final/
│   └── your_file_merged.docx
└── pipeline_metadata.json
```

## ⚙️ Cấu hình

### PP-StructureV3 Settings
- Mặc định sử dụng CPU, có thể chuyển sang GPU bằng cách sửa trong code
- Model sẽ được tự động download lần đầu chạy

### DOCX Conversion
- Ưu tiên sử dụng Pandoc nếu có
- Fallback về python-docx nếu Pandoc không có
- Hỗ trợ headers, lists, tables cơ bản

### Merge Options
- Tự động thêm page breaks giữa các file
- Thêm headers cho mỗi section
- Tạo title page với danh sách files

## 🐛 Troubleshooting

### Lỗi import PaddleOCR
```bash
pip install paddlepaddle paddleocr
```

### Lỗi CUDA (nếu có GPU)
```bash
pip install paddlepaddle-gpu
```

### Lỗi python-docx
```bash
pip install python-docx
```

### Pandoc không hoạt động
- Kiểm tra Pandoc đã cài đặt: `pandoc --version`
- Pipeline sẽ tự động fallback về python-docx

## 📝 Ví dụ sử dụng

### Ví dụ 1: Xử lý 1 file PDF
```python
from pdf_to_docx_pipeline import PDFToDocxPipeline

pipeline = PDFToDocxPipeline("./my_output")
result = pipeline.process_single_pdf("document.pdf", "Final_Document")

if result["success"]:
    print(f"Thành công! File: {result['final_output']}")
else:
    print(f"Lỗi: {result['error']}")
```

### Ví dụ 2: Batch processing
```python
pdf_files = ["doc1.pdf", "doc2.pdf", "doc3.pdf"]
result = pipeline.process_multiple_pdfs(pdf_files)

print(f"Xử lý {result['successful_files']}/{result['total_files']} files")
```

## 🔍 Monitoring & Logs

Pipeline tự động tạo metadata files để theo dõi:
- `metadata.json`: Thông tin chi tiết từng bước
- `conversion_metadata.json`: Kết quả convert MD → DOCX  
- `merge_metadata.json`: Thông tin merge DOCX
- `pipeline_metadata.json`: Tổng quan toàn bộ pipeline

## 🤝 Đóng góp

1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 🙏 Credits

- [PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR) - OCR engine
- [python-docx](https://python-docx.readthedocs.io/) - DOCX processing
- [Pandoc](https://pandoc.org/) - Document conversion
- [Markdown](https://python-markdown.github.io/) - Markdown processing
