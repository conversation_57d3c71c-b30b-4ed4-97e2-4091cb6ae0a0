�}q (X   conv2d_0.w_0q}q(X   structured_nameqX   backbone.conv1._conv.weightqX
   stop_gradientq�X	   trainableq�uX   batch_norm_0.w_0q}q(hX!   backbone.conv1._batch_norm.weightq	h�h�uX   batch_norm_0.b_0q
}q(hX   backbone.conv1._batch_norm.biasqh�h�uX   batch_norm_0.w_1q
}q(hX    backbone.conv1._batch_norm._meanqh�h�uX   batch_norm_0.w_2q}q(hX$   backbone.conv1._batch_norm._varianceqh�h�uX   conv2d_1.w_0q}q(hX2   backbone.block_list.0._depthwise_conv._conv.weightqh�h�uX   batch_norm_1.w_0q}q(hX8   backbone.block_list.0._depthwise_conv._batch_norm.weightqh�h�uX   batch_norm_1.b_0q}q(hX6   backbone.block_list.0._depthwise_conv._batch_norm.biasqh�h�uX   batch_norm_1.w_1q}q(hX7   backbone.block_list.0._depthwise_conv._batch_norm._meanqh�h�uX   batch_norm_1.w_2q}q (hX;   backbone.block_list.0._depthwise_conv._batch_norm._varianceq!h�h�uX   conv2d_2.w_0q"}q#(hX2   backbone.block_list.0._pointwise_conv._conv.weightq$h�h�uX   batch_norm_2.w_0q%}q&(hX8   backbone.block_list.0._pointwise_conv._batch_norm.weightq'h�h�uX   batch_norm_2.b_0q(}q)(hX6   backbone.block_list.0._pointwise_conv._batch_norm.biasq*h�h�uX   batch_norm_2.w_1q+}q,(hX7   backbone.block_list.0._pointwise_conv._batch_norm._meanq-h�h�uX   batch_norm_2.w_2q.}q/(hX;   backbone.block_list.0._pointwise_conv._batch_norm._varianceq0h�h�uX   conv2d_3.w_0q1}q2(hX2   backbone.block_list.1._depthwise_conv._conv.weightq3h�h�uX   batch_norm_3.w_0q4}q5(hX8   backbone.block_list.1._depthwise_conv._batch_norm.weightq6h�h�uX   batch_norm_3.b_0q7}q8(hX6   backbone.block_list.1._depthwise_conv._batch_norm.biasq9h�h�uX   batch_norm_3.w_1q:}q;(hX7   backbone.block_list.1._depthwise_conv._batch_norm._meanq<h�h�uX   batch_norm_3.w_2q=}q>(hX;   backbone.block_list.1._depthwise_conv._batch_norm._varianceq?h�h�uX   conv2d_4.w_0q@}qA(hX2   backbone.block_list.1._pointwise_conv._conv.weightqBh�h�uX   batch_norm_4.w_0qC}qD(hX8   backbone.block_list.1._pointwise_conv._batch_norm.weightqEh�h�uX   batch_norm_4.b_0qF}qG(hX6   backbone.block_list.1._pointwise_conv._batch_norm.biasqHh�h�uX   batch_norm_4.w_1qI}qJ(hX7   backbone.block_list.1._pointwise_conv._batch_norm._meanqKh�h�uX   batch_norm_4.w_2qL}qM(hX;   backbone.block_list.1._pointwise_conv._batch_norm._varianceqNh�h�uX   conv2d_5.w_0qO}qP(hX2   backbone.block_list.2._depthwise_conv._conv.weightqQh�h�uX   batch_norm_5.w_0qR}qS(hX8   backbone.block_list.2._depthwise_conv._batch_norm.weightqTh�h�uX   batch_norm_5.b_0qU}qV(hX6   backbone.block_list.2._depthwise_conv._batch_norm.biasqWh�h�uX   batch_norm_5.w_1qX}qY(hX7   backbone.block_list.2._depthwise_conv._batch_norm._meanqZh�h�uX   batch_norm_5.w_2q[}q\(hX;   backbone.block_list.2._depthwise_conv._batch_norm._varianceq]h�h�uX   conv2d_6.w_0q^}q_(hX2   backbone.block_list.2._pointwise_conv._conv.weightq`h�h�uX   batch_norm_6.w_0qa}qb(hX8   backbone.block_list.2._pointwise_conv._batch_norm.weightqch�h�uX   batch_norm_6.b_0qd}qe(hX6   backbone.block_list.2._pointwise_conv._batch_norm.biasqfh�h�uX   batch_norm_6.w_1qg}qh(hX7   backbone.block_list.2._pointwise_conv._batch_norm._meanqih�h�uX   batch_norm_6.w_2qj}qk(hX;   backbone.block_list.2._pointwise_conv._batch_norm._varianceqlh�h�uX   conv2d_7.w_0qm}qn(hX2   backbone.block_list.3._depthwise_conv._conv.weightqoh�h�uX   batch_norm_7.w_0qp}qq(hX8   backbone.block_list.3._depthwise_conv._batch_norm.weightqrh�h�uX   batch_norm_7.b_0qs}qt(hX6   backbone.block_list.3._depthwise_conv._batch_norm.biasquh�h�uX   batch_norm_7.w_1qv}qw(hX7   backbone.block_list.3._depthwise_conv._batch_norm._meanqxh�h�uX   batch_norm_7.w_2qy}qz(hX;   backbone.block_list.3._depthwise_conv._batch_norm._varianceq{h�h�uX   conv2d_8.w_0q|}q}(hX2   backbone.block_list.3._pointwise_conv._conv.weightq~h�h�uX   batch_norm_8.w_0q}q�(hX8   backbone.block_list.3._pointwise_conv._batch_norm.weightq�h�h�uX   batch_norm_8.b_0q�}q�(hX6   backbone.block_list.3._pointwise_conv._batch_norm.biasq�h�h�uX   batch_norm_8.w_1q�}q�(hX7   backbone.block_list.3._pointwise_conv._batch_norm._meanq�h�h�uX   batch_norm_8.w_2q�}q�(hX;   backbone.block_list.3._pointwise_conv._batch_norm._varianceq�h�h�uX   conv2d_9.w_0q�}q�(hX2   backbone.block_list.4._depthwise_conv._conv.weightq�h�h�uX   batch_norm_9.w_0q�}q�(hX8   backbone.block_list.4._depthwise_conv._batch_norm.weightq�h�h�uX   batch_norm_9.b_0q�}q�(hX6   backbone.block_list.4._depthwise_conv._batch_norm.biasq�h�h�uX   batch_norm_9.w_1q�}q�(hX7   backbone.block_list.4._depthwise_conv._batch_norm._meanq�h�h�uX   batch_norm_9.w_2q�}q�(hX;   backbone.block_list.4._depthwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_10.w_0q�}q�(hX2   backbone.block_list.4._pointwise_conv._conv.weightq�h�h�uX   batch_norm_10.w_0q�}q�(hX8   backbone.block_list.4._pointwise_conv._batch_norm.weightq�h�h�uX   batch_norm_10.b_0q�}q�(hX6   backbone.block_list.4._pointwise_conv._batch_norm.biasq�h�h�uX   batch_norm_10.w_1q�}q�(hX7   backbone.block_list.4._pointwise_conv._batch_norm._meanq�h�h�uX   batch_norm_10.w_2q�}q�(hX;   backbone.block_list.4._pointwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_11.w_0q�}q�(hX2   backbone.block_list.5._depthwise_conv._conv.weightq�h�h�uX   batch_norm_11.w_0q�}q�(hX8   backbone.block_list.5._depthwise_conv._batch_norm.weightq�h�h�uX   batch_norm_11.b_0q�}q�(hX6   backbone.block_list.5._depthwise_conv._batch_norm.biasq�h�h�uX   batch_norm_11.w_1q�}q�(hX7   backbone.block_list.5._depthwise_conv._batch_norm._meanq�h�h�uX   batch_norm_11.w_2q�}q�(hX;   backbone.block_list.5._depthwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_12.w_0q�}q�(hX2   backbone.block_list.5._pointwise_conv._conv.weightq�h�h�uX   batch_norm_12.w_0q�}q�(hX8   backbone.block_list.5._pointwise_conv._batch_norm.weightq�h�h�uX   batch_norm_12.b_0q�}q�(hX6   backbone.block_list.5._pointwise_conv._batch_norm.biasq�h�h�uX   batch_norm_12.w_1q�}q�(hX7   backbone.block_list.5._pointwise_conv._batch_norm._meanq�h�h�uX   batch_norm_12.w_2q�}q�(hX;   backbone.block_list.5._pointwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_13.w_0q�}q�(hX2   backbone.block_list.6._depthwise_conv._conv.weightq�h�h�uX   batch_norm_13.w_0q�}q�(hX8   backbone.block_list.6._depthwise_conv._batch_norm.weightq�h�h�uX   batch_norm_13.b_0q�}q�(hX6   backbone.block_list.6._depthwise_conv._batch_norm.biasq�h�h�uX   batch_norm_13.w_1q�}q�(hX7   backbone.block_list.6._depthwise_conv._batch_norm._meanq�h�h�uX   batch_norm_13.w_2q�}q�(hX;   backbone.block_list.6._depthwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_14.w_0q�}q�(hX2   backbone.block_list.6._pointwise_conv._conv.weightq�h�h�uX   batch_norm_14.w_0q�}q�(hX8   backbone.block_list.6._pointwise_conv._batch_norm.weightq�h�h�uX   batch_norm_14.b_0q�}q�(hX6   backbone.block_list.6._pointwise_conv._batch_norm.biasq�h�h�uX   batch_norm_14.w_1q�}q�(hX7   backbone.block_list.6._pointwise_conv._batch_norm._meanq�h�h�uX   batch_norm_14.w_2q�}q�(hX;   backbone.block_list.6._pointwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_15.w_0q�}q�(hX2   backbone.block_list.7._depthwise_conv._conv.weightq�h�h�uX   batch_norm_15.w_0q�}q�(hX8   backbone.block_list.7._depthwise_conv._batch_norm.weightq�h�h�uX   batch_norm_15.b_0q�}q�(hX6   backbone.block_list.7._depthwise_conv._batch_norm.biasq�h�h�uX   batch_norm_15.w_1q�}q�(hX7   backbone.block_list.7._depthwise_conv._batch_norm._meanq�h�h�uX   batch_norm_15.w_2q�}q�(hX;   backbone.block_list.7._depthwise_conv._batch_norm._varianceq�h�h�uX
   conv2d_16.w_0q�}q�(hX2   backbone.block_list.7._pointwise_conv._conv.weightq�h�h�uX   batch_norm_16.w_0q�}q�(hX8   backbone.block_list.7._pointwise_conv._batch_norm.weightq�h�h�uX   batch_norm_16.b_0q�}q�(hX6   backbone.block_list.7._pointwise_conv._batch_norm.biasq�h�h�uX   batch_norm_16.w_1q�}q�(hX7   backbone.block_list.7._pointwise_conv._batch_norm._meanq�h�h�uX   batch_norm_16.w_2r   }r  (hX;   backbone.block_list.7._pointwise_conv._batch_norm._variancer  h�h�uX
   conv2d_17.w_0r  }r  (hX2   backbone.block_list.8._depthwise_conv._conv.weightr  h�h�uX   batch_norm_17.w_0r  }r  (hX8   backbone.block_list.8._depthwise_conv._batch_norm.weightr  h�h�uX   batch_norm_17.b_0r	  }r
  (hX6   backbone.block_list.8._depthwise_conv._batch_norm.biasr  h�h�uX   batch_norm_17.w_1r  }r
  (hX7   backbone.block_list.8._depthwise_conv._batch_norm._meanr  h�h�uX   batch_norm_17.w_2r  }r  (hX;   backbone.block_list.8._depthwise_conv._batch_norm._variancer  h�h�uX
   conv2d_18.w_0r  }r  (hX2   backbone.block_list.8._pointwise_conv._conv.weightr  h�h�uX   batch_norm_18.w_0r  }r  (hX8   backbone.block_list.8._pointwise_conv._batch_norm.weightr  h�h�uX   batch_norm_18.b_0r  }r  (hX6   backbone.block_list.8._pointwise_conv._batch_norm.biasr  h�h�uX   batch_norm_18.w_1r  }r  (hX7   backbone.block_list.8._pointwise_conv._batch_norm._meanr  h�h�uX   batch_norm_18.w_2r  }r  (hX;   backbone.block_list.8._pointwise_conv._batch_norm._variancer   h�h�uX
   conv2d_19.w_0r!  }r"  (hX2   backbone.block_list.9._depthwise_conv._conv.weightr#  h�h�uX   batch_norm_19.w_0r$  }r%  (hX8   backbone.block_list.9._depthwise_conv._batch_norm.weightr&  h�h�uX   batch_norm_19.b_0r'  }r(  (hX6   backbone.block_list.9._depthwise_conv._batch_norm.biasr)  h�h�uX   batch_norm_19.w_1r*  }r+  (hX7   backbone.block_list.9._depthwise_conv._batch_norm._meanr,  h�h�uX   batch_norm_19.w_2r-  }r.  (hX;   backbone.block_list.9._depthwise_conv._batch_norm._variancer/  h�h�uX
   conv2d_20.w_0r0  }r1  (hX2   backbone.block_list.9._pointwise_conv._conv.weightr2  h�h�uX   batch_norm_20.w_0r3  }r4  (hX8   backbone.block_list.9._pointwise_conv._batch_norm.weightr5  h�h�uX   batch_norm_20.b_0r6  }r7  (hX6   backbone.block_list.9._pointwise_conv._batch_norm.biasr8  h�h�uX   batch_norm_20.w_1r9  }r:  (hX7   backbone.block_list.9._pointwise_conv._batch_norm._meanr;  h�h�uX   batch_norm_20.w_2r<  }r=  (hX;   backbone.block_list.9._pointwise_conv._batch_norm._variancer>  h�h�uX
   conv2d_21.w_0r?  }r@  (hX3   backbone.block_list.10._depthwise_conv._conv.weightrA  h�h�uX   batch_norm_21.w_0rB  }rC  (hX9   backbone.block_list.10._depthwise_conv._batch_norm.weightrD  h�h�uX   batch_norm_21.b_0rE  }rF  (hX7   backbone.block_list.10._depthwise_conv._batch_norm.biasrG  h�h�uX   batch_norm_21.w_1rH  }rI  (hX8   backbone.block_list.10._depthwise_conv._batch_norm._meanrJ  h�h�uX   batch_norm_21.w_2rK  }rL  (hX<   backbone.block_list.10._depthwise_conv._batch_norm._variancerM  h�h�uX
   conv2d_22.w_0rN  }rO  (hX3   backbone.block_list.10._pointwise_conv._conv.weightrP  h�h�uX   batch_norm_22.w_0rQ  }rR  (hX9   backbone.block_list.10._pointwise_conv._batch_norm.weightrS  h�h�uX   batch_norm_22.b_0rT  }rU  (hX7   backbone.block_list.10._pointwise_conv._batch_norm.biasrV  h�h�uX   batch_norm_22.w_1rW  }rX  (hX8   backbone.block_list.10._pointwise_conv._batch_norm._meanrY  h�h�uX   batch_norm_22.w_2rZ  }r[  (hX<   backbone.block_list.10._pointwise_conv._batch_norm._variancer\  h�h�uX
   conv2d_23.w_0r]  }r^  (hX3   backbone.block_list.11._depthwise_conv._conv.weightr_  h�h�uX   batch_norm_23.w_0r`  }ra  (hX9   backbone.block_list.11._depthwise_conv._batch_norm.weightrb  h�h�uX   batch_norm_23.b_0rc  }rd  (hX7   backbone.block_list.11._depthwise_conv._batch_norm.biasre  h�h�uX   batch_norm_23.w_1rf  }rg  (hX8   backbone.block_list.11._depthwise_conv._batch_norm._meanrh  h�h�uX   batch_norm_23.w_2ri  }rj  (hX<   backbone.block_list.11._depthwise_conv._batch_norm._variancerk  h�h�uX
   conv2d_24.w_0rl  }rm  (hX'   backbone.block_list.11._se.conv1.weightrn  h�h�uX
   conv2d_24.b_0ro  }rp  (hX%   backbone.block_list.11._se.conv1.biasrq  h�h�uX
   conv2d_25.w_0rr  }rs  (hX'   backbone.block_list.11._se.conv2.weightrt  h�h�uX
   conv2d_25.b_0ru  }rv  (hX%   backbone.block_list.11._se.conv2.biasrw  h�h�uX
   conv2d_26.w_0rx  }ry  (hX3   backbone.block_list.11._pointwise_conv._conv.weightrz  h�h�uX   batch_norm_24.w_0r{  }r|  (hX9   backbone.block_list.11._pointwise_conv._batch_norm.weightr}  h�h�uX   batch_norm_24.b_0r~  }r  (hX7   backbone.block_list.11._pointwise_conv._batch_norm.biasr�  h�h�uX   batch_norm_24.w_1r�  }r�  (hX8   backbone.block_list.11._pointwise_conv._batch_norm._meanr�  h�h�uX   batch_norm_24.w_2r�  }r�  (hX<   backbone.block_list.11._pointwise_conv._batch_norm._variancer�  h�h�uX
   conv2d_27.w_0r�  }r�  (hX3   backbone.block_list.12._depthwise_conv._conv.weightr�  h�h�uX   batch_norm_25.w_0r�  }r�  (hX9   backbone.block_list.12._depthwise_conv._batch_norm.weightr�  h�h�uX   batch_norm_25.b_0r�  }r�  (hX7   backbone.block_list.12._depthwise_conv._batch_norm.biasr�  h�h�uX   batch_norm_25.w_1r�  }r�  (hX8   backbone.block_list.12._depthwise_conv._batch_norm._meanr�  h�h�uX   batch_norm_25.w_2r�  }r�  (hX<   backbone.block_list.12._depthwise_conv._batch_norm._variancer�  h�h�uX
   conv2d_28.w_0r�  }r�  (hX'   backbone.block_list.12._se.conv1.weightr�  h�h�uX
   conv2d_28.b_0r�  }r�  (hX%   backbone.block_list.12._se.conv1.biasr�  h�h�uX
   conv2d_29.w_0r�  }r�  (hX'   backbone.block_list.12._se.conv2.weightr�  h�h�uX
   conv2d_29.b_0r�  }r�  (hX%   backbone.block_list.12._se.conv2.biasr�  h�h�uX
   conv2d_30.w_0r�  }r�  (hX3   backbone.block_list.12._pointwise_conv._conv.weightr�  h�h�uX   batch_norm_26.w_0r�  }r�  (hX9   backbone.block_list.12._pointwise_conv._batch_norm.weightr�  h�h�uX   batch_norm_26.b_0r�  }r�  (hX7   backbone.block_list.12._pointwise_conv._batch_norm.biasr�  h�h�uX   batch_norm_26.w_1r�  }r�  (hX8   backbone.block_list.12._pointwise_conv._batch_norm._meanr�  h�h�uX   batch_norm_26.w_2r�  }r�  (hX<   backbone.block_list.12._pointwise_conv._batch_norm._variancer�  h�h�uX
   conv2d_31.w_0r�  }r�  (hX*   head.ctc_encoder.encoder.conv1.conv.weightr�  h�h�uX   batch_norm2d_0.w_0r�  }r�  (hX*   head.ctc_encoder.encoder.conv1.norm.weightr�  h�h�uX   batch_norm2d_0.b_0r�  }r�  (hX(   head.ctc_encoder.encoder.conv1.norm.biasr�  h�h�uX   batch_norm2d_0.w_1r�  }r�  (hX)   head.ctc_encoder.encoder.conv1.norm._meanr�  h�h�uX   batch_norm2d_0.w_2r�  }r�  (hX-   head.ctc_encoder.encoder.conv1.norm._variancer�  h�h�uX
   conv2d_32.w_0r�  }r�  (hX*   head.ctc_encoder.encoder.conv2.conv.weightr�  h�h�uX   batch_norm2d_1.w_0r�  }r�  (hX*   head.ctc_encoder.encoder.conv2.norm.weightr�  h�h�uX   batch_norm2d_1.b_0r�  }r�  (hX(   head.ctc_encoder.encoder.conv2.norm.biasr�  h�h�uX   batch_norm2d_1.w_1r�  }r�  (hX)   head.ctc_encoder.encoder.conv2.norm._meanr�  h�h�uX   batch_norm2d_1.w_2r�  }r�  (hX-   head.ctc_encoder.encoder.conv2.norm._variancer�  h�h�uX   layer_norm_0.w_0r�  }r�  (hX2   head.ctc_encoder.encoder.svtr_block.0.norm1.weightr�  h�h�uX   layer_norm_0.b_0r�  }r�  (hX0   head.ctc_encoder.encoder.svtr_block.0.norm1.biasr�  h�h�uX   linear_0.w_0r�  }r�  (hX6   head.ctc_encoder.encoder.svtr_block.0.mixer.qkv.weightr�  h�h�uX   linear_0.b_0r�  }r�  (hX4   head.ctc_encoder.encoder.svtr_block.0.mixer.qkv.biasr�  h�h�uX   linear_1.w_0r�  }r�  (hX7   head.ctc_encoder.encoder.svtr_block.0.mixer.proj.weightr�  h�h�uX   linear_1.b_0r�  }r�  (hX5   head.ctc_encoder.encoder.svtr_block.0.mixer.proj.biasr�  h�h�uX   layer_norm_1.w_0r�  }r�  (hX2   head.ctc_encoder.encoder.svtr_block.0.norm2.weightr�  h�h�uX   layer_norm_1.b_0r�  }r�  (hX0   head.ctc_encoder.encoder.svtr_block.0.norm2.biasr�  h�h�uX   linear_2.w_0r�  }r�  (hX4   head.ctc_encoder.encoder.svtr_block.0.mlp.fc1.weightr�  h�h�uX   linear_2.b_0r�  }r�  (hX2   head.ctc_encoder.encoder.svtr_block.0.mlp.fc1.biasr�  h�h�uX   linear_3.w_0r�  }r�  (hX4   head.ctc_encoder.encoder.svtr_block.0.mlp.fc2.weightr�  h�h�uX   linear_3.b_0r�  }r�  (hX2   head.ctc_encoder.encoder.svtr_block.0.mlp.fc2.biasr�  h�h�uX   layer_norm_2.w_0r�  }r�  (hX2   head.ctc_encoder.encoder.svtr_block.1.norm1.weightr�  h�h�uX   layer_norm_2.b_0r�  }r�  (hX0   head.ctc_encoder.encoder.svtr_block.1.norm1.biasr�  h�h�uX   linear_4.w_0r�  }r�  (hX6   head.ctc_encoder.encoder.svtr_block.1.mixer.qkv.weightr�  h�h�uX   linear_4.b_0r�  }r�  (hX4   head.ctc_encoder.encoder.svtr_block.1.mixer.qkv.biasr�  h�h�uX   linear_5.w_0r�  }r   (hX7   head.ctc_encoder.encoder.svtr_block.1.mixer.proj.weightr  h�h�uX   linear_5.b_0r  }r  (hX5   head.ctc_encoder.encoder.svtr_block.1.mixer.proj.biasr  h�h�uX   layer_norm_3.w_0r  }r  (hX2   head.ctc_encoder.encoder.svtr_block.1.norm2.weightr  h�h�uX   layer_norm_3.b_0r  }r	  (hX0   head.ctc_encoder.encoder.svtr_block.1.norm2.biasr
  h�h�uX   linear_6.w_0r  }r  (hX4   head.ctc_encoder.encoder.svtr_block.1.mlp.fc1.weightr
  h�h�uX   linear_6.b_0r  }r  (hX2   head.ctc_encoder.encoder.svtr_block.1.mlp.fc1.biasr  h�h�uX   linear_7.w_0r  }r  (hX4   head.ctc_encoder.encoder.svtr_block.1.mlp.fc2.weightr  h�h�uX   linear_7.b_0r  }r  (hX2   head.ctc_encoder.encoder.svtr_block.1.mlp.fc2.biasr  h�h�uX   layer_norm_4.w_0r  }r  (hX$   head.ctc_encoder.encoder.norm.weightr  h�h�uX   layer_norm_4.b_0r  }r  (hX"   head.ctc_encoder.encoder.norm.biasr  h�h�uX
   conv2d_33.w_0r  }r  (hX*   head.ctc_encoder.encoder.conv3.conv.weightr  h�h�uX   batch_norm2d_2.w_0r   }r!  (hX*   head.ctc_encoder.encoder.conv3.norm.weightr"  h�h�uX   batch_norm2d_2.b_0r#  }r$  (hX(   head.ctc_encoder.encoder.conv3.norm.biasr%  h�h�uX   batch_norm2d_2.w_1r&  }r'  (hX)   head.ctc_encoder.encoder.conv3.norm._meanr(  h�h�uX   batch_norm2d_2.w_2r)  }r*  (hX-   head.ctc_encoder.encoder.conv3.norm._variancer+  h�h�uX
   conv2d_34.w_0r,  }r-  (hX*   head.ctc_encoder.encoder.conv4.conv.weightr.  h�h�uX   batch_norm2d_3.w_0r/  }r0  (hX*   head.ctc_encoder.encoder.conv4.norm.weightr1  h�h�uX   batch_norm2d_3.b_0r2  }r3  (hX(   head.ctc_encoder.encoder.conv4.norm.biasr4  h�h�uX   batch_norm2d_3.w_1r5  }r6  (hX)   head.ctc_encoder.encoder.conv4.norm._meanr7  h�h�uX   batch_norm2d_3.w_2r8  }r9  (hX-   head.ctc_encoder.encoder.conv4.norm._variancer:  h�h�uX
   conv2d_35.w_0r;  }r<  (hX,   head.ctc_encoder.encoder.conv1x1.conv.weightr=  h�h�uX   batch_norm2d_4.w_0r>  }r?  (hX,   head.ctc_encoder.encoder.conv1x1.norm.weightr@  h�h�uX   batch_norm2d_4.b_0rA  }rB  (hX*   head.ctc_encoder.encoder.conv1x1.norm.biasrC  h�h�uX   batch_norm2d_4.w_1rD  }rE  (hX+   head.ctc_encoder.encoder.conv1x1.norm._meanrF  h�h�uX   batch_norm2d_4.w_2rG  }rH  (hX/   head.ctc_encoder.encoder.conv1x1.norm._variancerI  h�h�uX   linear_8.w_0rJ  }rK  (hX   head.ctc_head.fc.weightrL  h�h�uX   linear_8.b_0rM  }rN  (hX   head.ctc_head.fc.biasrO  h�h�uX   lstm_cell_0.w_0rP  }rQ  (hX2   head.sar_head.encoder.rnn_encoder.0.cell.weight_ihrR  h�h�uX   lstm_cell_0.w_1rS  }rT  (hX2   head.sar_head.encoder.rnn_encoder.0.cell.weight_hhrU  h�h�uX   lstm_cell_0.b_0rV  }rW  (hX0   head.sar_head.encoder.rnn_encoder.0.cell.bias_ihrX  h�h�uX   lstm_cell_0.b_1rY  }rZ  (hX0   head.sar_head.encoder.rnn_encoder.0.cell.bias_hhr[  h�h�uX   lstm_cell_1.w_0r\  }r]  (hX2   head.sar_head.encoder.rnn_encoder.1.cell.weight_ihr^  h�h�uX   lstm_cell_1.w_1r_  }r`  (hX2   head.sar_head.encoder.rnn_encoder.1.cell.weight_hhra  h�h�uX   lstm_cell_1.b_0rb  }rc  (hX0   head.sar_head.encoder.rnn_encoder.1.cell.bias_ihrd  h�h�uX   lstm_cell_1.b_1re  }rf  (hX0   head.sar_head.encoder.rnn_encoder.1.cell.bias_hhrg  h�h�uX   linear_9.w_0rh  }ri  (hX#   head.sar_head.encoder.linear.weightrj  h�h�uX   linear_9.b_0rk  }rl  (hX!   head.sar_head.encoder.linear.biasrm  h�h�uX
   linear_10.w_0rn  }ro  (hX&   head.sar_head.decoder.conv1x1_1.weightrp  h�h�uX
   linear_10.b_0rq  }rr  (hX$   head.sar_head.decoder.conv1x1_1.biasrs  h�h�uX
   conv2d_36.w_0rt  }ru  (hX&   head.sar_head.decoder.conv3x3_1.weightrv  h�h�uX
   conv2d_36.b_0rw  }rx  (hX$   head.sar_head.decoder.conv3x3_1.biasry  h�h�uX
   linear_11.w_0rz  }r{  (hX&   head.sar_head.decoder.conv1x1_2.weightr|  h�h�uX
   linear_11.b_0r}  }r~  (hX$   head.sar_head.decoder.conv1x1_2.biasr  h�h�uX   lstm_cell_2.w_0r�  }r�  (hX2   head.sar_head.decoder.rnn_decoder.0.cell.weight_ihr�  h�h�uX   lstm_cell_2.w_1r�  }r�  (hX2   head.sar_head.decoder.rnn_decoder.0.cell.weight_hhr�  h�h�uX   lstm_cell_2.b_0r�  }r�  (hX0   head.sar_head.decoder.rnn_decoder.0.cell.bias_ihr�  h�h�uX   lstm_cell_2.b_1r�  }r�  (hX0   head.sar_head.decoder.rnn_decoder.0.cell.bias_hhr�  h�h�uX   lstm_cell_3.w_0r�  }r�  (hX2   head.sar_head.decoder.rnn_decoder.1.cell.weight_ihr�  h�h�uX   lstm_cell_3.w_1r�  }r�  (hX2   head.sar_head.decoder.rnn_decoder.1.cell.weight_hhr�  h�h�uX   lstm_cell_3.b_0r�  }r�  (hX0   head.sar_head.decoder.rnn_decoder.1.cell.bias_ihr�  h�h�uX   lstm_cell_3.b_1r�  }r�  (hX0   head.sar_head.decoder.rnn_decoder.1.cell.bias_hhr�  h�h�uX   embedding_0.w_0r�  }r�  (hX&   head.sar_head.decoder.embedding.weightr�  h�h�uX
   linear_12.w_0r�  }r�  (hX'   head.sar_head.decoder.prediction.weightr�  h�h�uX
   linear_12.b_0r�  }r�  (hX%   head.sar_head.decoder.prediction.biasr�  h�h�uu.