# Requirements cho PDF to DOCX Pipeline sử dụng PP-StructureV3

# Core OCR và PDF processing
paddleocr>=2.7.0
paddlepaddle>=2.5.0

# Document processing
python-docx>=0.8.11
markdown>=3.4.0

# Utilities
pathlib2>=2.3.7
Pillow>=9.0.0

# Optional: Pandoc alternative (nếu không có pandoc system-wide)
pypandoc>=1.11

# JSON và file handling (built-in, nhưng list để rõ ràng)
# json - built-in
# pathlib - built-in (Python 3.4+)
# subprocess - built-in
# argparse - built-in
# time - built-in
# re - built-in
# os - built-in

# Development và testing
pytest>=7.0.0
pytest-cov>=4.0.0
