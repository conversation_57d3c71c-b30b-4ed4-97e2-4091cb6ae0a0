from pathlib import Path
from paddleocr import PPStructureV3
from docx import Document
import pypandoc

# -----------------------
# C<PERSON>u hình
# -----------------------
input_file = "sample.pdf"  # "test_file.pdf"
output_path = Path("./output")
output_path.mkdir(parents=True, exist_ok=True)

pipeline = PPStructureV3()
output = pipeline.predict(input=input_file)

# -----------------------
# B1: Xuất từng page ra .md
# -----------------------
markdown_files = []
for i, res in enumerate(output, start=1):
    md_info = res.markdown
    md_text = md_info["markdown_text"]

    md_file = output_path / f"page_{i}.md"
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(md_text)
    markdown_files.append(md_file)

    # <PERSON><PERSON>t hình ảnh (nếu có)
    md_images = md_info.get("markdown_images", {})
    for path, image in md_images.items():
        file_path = output_path / path
        file_path.parent.mkdir(parents=True, exist_ok=True)
        image.save(file_path)

print(f"✅ Đã xuất {len(markdown_files)} file markdown.")

# -----------------------
# B2: Convert từng .md → .docx
# -----------------------
docx_files = []
for md_file in markdown_files:
    docx_file = md_file.with_suffix(".docx")
    pypandoc.convert_text(
        open(md_file, encoding="utf-8").read(),
        to="docx",
        format="md",
        outputfile=str(docx_file),
        extra_args=["--standalone"]
    )
    docx_files.append(docx_file)

print(f"✅ Đã convert {len(docx_files)} file docx.")

# -----------------------
# B3: Gộp tất cả DOCX lại thành 1 file
# -----------------------
merged_doc = Document()

for i, docx_file in enumerate(docx_files, start=1):
    sub_doc = Document(docx_file)
    if i > 1:
        merged_doc.add_page_break()  # ngăn cách page khi gộp
    for element in sub_doc.element.body:
        merged_doc.element.body.append(element)

final_docx = output_path / f"{Path(input_file).stem}_merged.docx"
merged_doc.save(final_docx)

print(f"✅ Hoàn thành! File cuối: {final_docx}")
