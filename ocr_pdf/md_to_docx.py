"""
<PERSON><PERSON><PERSON> để convert các file Markdown thành DOCX
Sử dụng pandoc hoặc python-docx + markdown libraries
"""

import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import markdown
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re
import os


class MarkdownToDocxConverter:
    def __init__(self):
        """Khởi tạo converter"""
        self.check_pandoc_available()
        
    def check_pandoc_available(self) -> bool:
        """Kiểm tra xem pandoc có sẵn không"""
        try:
            result = subprocess.run(['pandoc', '--version'], 
                                  capture_output=True, text=True)
            self.has_pandoc = result.returncode == 0
            if self.has_pandoc:
                print("✅ Pandoc có sẵn - sẽ sử dụng pandoc để convert")
            else:
                print("⚠️  Pandoc không có sẵn - sẽ sử dụng python-docx")
        except FileNotFoundError:
            self.has_pandoc = False
            print("⚠️  Pandoc không có sẵn - sẽ sử dụng python-docx")
        
        return self.has_pandoc
    
    def convert_md_to_docx_pandoc(self, md_file_path: str, docx_file_path: str) -> bool:
        """
        Convert MD sang DOCX sử dụng pandoc
        
        Args:
            md_file_path: Đường dẫn file MD
            docx_file_path: Đường dẫn file DOCX output
            
        Returns:
            True nếu thành công
        """
        try:
            cmd = [
                'pandoc',
                str(md_file_path),
                '-o', str(docx_file_path),
                '--from', 'markdown',
                '--to', 'docx'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                print(f"Lỗi pandoc: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Lỗi khi chạy pandoc: {e}")
            return False
    
    def convert_md_to_docx_python(self, md_file_path: str, docx_file_path: str) -> bool:
        """
        Convert MD sang DOCX sử dụng python-docx
        
        Args:
            md_file_path: Đường dẫn file MD
            docx_file_path: Đường dẫn file DOCX output
            
        Returns:
            True nếu thành công
        """
        try:
            # Đọc file markdown
            with open(md_file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # Tạo document mới
            doc = Document()
            
            # Parse markdown thành HTML rồi xử lý
            html = markdown.markdown(md_content)
            
            # Xử lý từng dòng của markdown
            lines = md_content.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    # Thêm paragraph trống
                    doc.add_paragraph()
                    continue
                
                # Xử lý headers
                if line.startswith('#'):
                    level = len(line) - len(line.lstrip('#'))
                    text = line.lstrip('#').strip()
                    
                    if level == 1:
                        heading = doc.add_heading(text, level=1)
                    elif level == 2:
                        heading = doc.add_heading(text, level=2)
                    elif level == 3:
                        heading = doc.add_heading(text, level=3)
                    else:
                        heading = doc.add_heading(text, level=4)
                
                # Xử lý list items
                elif line.startswith('- ') or line.startswith('* '):
                    text = line[2:].strip()
                    doc.add_paragraph(text, style='List Bullet')
                
                elif re.match(r'^\d+\.\s', line):
                    text = re.sub(r'^\d+\.\s', '', line)
                    doc.add_paragraph(text, style='List Number')
                
                # Xử lý table (cơ bản)
                elif '|' in line and line.count('|') >= 2:
                    # Bỏ qua table headers và separators cho đơn giản
                    if not line.startswith('|---') and not line.startswith('---'):
                        cells = [cell.strip() for cell in line.split('|')[1:-1]]
                        if cells:
                            # Thêm như paragraph thông thường
                            doc.add_paragraph(' | '.join(cells))
                
                # Paragraph thông thường
                else:
                    doc.add_paragraph(line)
            
            # Lưu file
            doc.save(docx_file_path)
            return True
            
        except Exception as e:
            print(f"Lỗi khi convert bằng python-docx: {e}")
            return False
    
    def convert_single_md_to_docx(self, md_file_path: str, docx_file_path: str) -> bool:
        """
        Convert 1 file MD sang DOCX
        
        Args:
            md_file_path: Đường dẫn file MD
            docx_file_path: Đường dẫn file DOCX output
            
        Returns:
            True nếu thành công
        """
        # Tạo thư mục output nếu chưa có
        Path(docx_file_path).parent.mkdir(parents=True, exist_ok=True)
        
        if self.has_pandoc:
            success = self.convert_md_to_docx_pandoc(md_file_path, docx_file_path)
            if success:
                return True
            else:
                print("Pandoc thất bại, thử python-docx...")
        
        return self.convert_md_to_docx_python(md_file_path, docx_file_path)
    
    def convert_multiple_md_to_docx(self, input_dir: str, output_dir: str = None) -> Dict[str, Any]:
        """
        Convert tất cả file MD trong thư mục thành DOCX
        
        Args:
            input_dir: Thư mục chứa các file MD
            output_dir: Thư mục output (mặc định là input_dir/docx)
            
        Returns:
            Dict chứa thông tin về quá trình convert
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"Thư mục không tồn tại: {input_dir}")
        
        if output_dir is None:
            output_path = input_path / "docx"
        else:
            output_path = Path(output_dir)
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Tìm tất cả file .md
        md_files = list(input_path.glob("*.md"))
        if not md_files:
            print(f"Không tìm thấy file .md nào trong {input_dir}")
            return {"success": False, "message": "No MD files found"}
        
        results = []
        successful_conversions = 0
        
        print(f"Tìm thấy {len(md_files)} file MD để convert...")
        
        for md_file in md_files:
            # Bỏ qua file index.md
            if md_file.name == "index.md":
                continue
                
            docx_filename = md_file.stem + ".docx"
            docx_file_path = output_path / docx_filename
            
            print(f"Converting: {md_file.name} -> {docx_filename}")
            
            success = self.convert_single_md_to_docx(str(md_file), str(docx_file_path))
            
            result = {
                "md_file": str(md_file),
                "docx_file": str(docx_file_path),
                "success": success,
                "file_size": docx_file_path.stat().st_size if success else 0
            }
            
            results.append(result)
            
            if success:
                successful_conversions += 1
                print(f"✅ Thành công: {docx_filename}")
            else:
                print(f"❌ Thất bại: {md_file.name}")
        
        # Tạo metadata
        metadata = {
            "input_directory": str(input_path),
            "output_directory": str(output_path),
            "total_md_files": len(md_files),
            "successful_conversions": successful_conversions,
            "failed_conversions": len(md_files) - successful_conversions,
            "conversion_results": results
        }
        
        # Lưu metadata
        metadata_file = output_path / "conversion_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 Kết quả:")
        print(f"- Thành công: {successful_conversions}/{len(md_files)}")
        print(f"- Metadata lưu tại: {metadata_file}")
        
        return metadata


def main():
    """Hàm main để test"""
    converter = MarkdownToDocxConverter()
    
    # Test với thư mục output từ script trước
    test_dirs = ["./output_md/sample", "./output_md/test_file"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            print(f"\n{'='*50}")
            print(f"Convert MD files trong: {test_dir}")
            print(f"{'='*50}")
            
            try:
                metadata = converter.convert_multiple_md_to_docx(test_dir)
                print(f"✅ Hoàn thành convert {test_dir}")
            except Exception as e:
                print(f"❌ Lỗi khi convert {test_dir}: {e}")
        else:
            print(f"⚠️  Thư mục không tồn tại: {test_dir}")


if __name__ == "__main__":
    main()
