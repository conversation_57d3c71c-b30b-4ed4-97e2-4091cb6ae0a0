"""
Demo script để test pipeline PDF to DOCX
"""

from pathlib import Path
import sys
import os

# Thêm current directory vào Python path
sys.path.append(str(Path(__file__).parent))

try:
    from pdf_to_docx_pipeline import PDFToDocxPipeline
    print("✅ Import thành công!")
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("Vui lòng cài đặt dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def demo_pipeline():
    """Demo chạy pipeline"""
    print("🚀 DEMO PDF TO DOCX PIPELINE")
    print("=" * 50)
    
    # Tìm file PDF để test
    pdf_files = []
    for pdf_name in ["sample.pdf", "test_file.pdf"]:
        if Path(pdf_name).exists():
            pdf_files.append(pdf_name)
            print(f"✅ Tìm thấy file: {pdf_name}")
        else:
            print(f"⚠️  Không tìm thấy: {pdf_name}")
    
    if not pdf_files:
        print("\n❌ Không tìm thấy file PDF nào để test!")
        print("Vui lòng đặt file PDF vào thư mục hiện tại với tên:")
        print("- sample.pdf")
        print("- test_file.pdf")
        print("hoặc file PDF bất kỳ")
        return
    
    # Khởi tạo pipeline
    pipeline = PDFToDocxPipeline("./demo_output")
    
    print(f"\n📄 Sẽ xử lý {len(pdf_files)} file(s):")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")
    
    # Chạy pipeline
    try:
        if len(pdf_files) == 1:
            result = pipeline.process_single_pdf(pdf_files[0])
            
            if result["success"]:
                print(f"\n🎉 THÀNH CÔNG!")
                print(f"📄 File cuối cùng: {result['final_output']}")
                print(f"⏱️  Thời gian: {result['total_duration']:.2f}s")
                
                # Hiển thị thông tin chi tiết
                print(f"\n📊 CHI TIẾT:")
                for step_name, step_info in result["steps"].items():
                    print(f"  {step_name}: {step_info['duration']:.2f}s")
            else:
                print(f"\n❌ THẤT BẠI: {result.get('error', 'Unknown error')}")
        else:
            result = pipeline.process_multiple_pdfs(pdf_files)
            print(f"\n🎉 HOÀN THÀNH!")
            print(f"✅ Thành công: {result['successful_files']}/{result['total_files']}")
            print(f"⏱️  Tổng thời gian: {result['total_duration']:.2f}s")
            
    except Exception as e:
        print(f"\n❌ LỖI: {e}")
        import traceback
        traceback.print_exc()


def check_dependencies():
    """Kiểm tra dependencies"""
    print("🔍 KIỂM TRA DEPENDENCIES")
    print("=" * 30)
    
    dependencies = [
        ("paddleocr", "PaddleOCR"),
        ("paddlepaddle", "PaddlePaddle"),
        ("docx", "python-docx"),
        ("markdown", "Markdown"),
        ("PIL", "Pillow")
    ]
    
    missing = []
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - THIẾU")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️  Thiếu {len(missing)} dependencies:")
        for dep in missing:
            print(f"  - {dep}")
        print("\nCài đặt bằng lệnh:")
        print("pip install -r requirements.txt")
        return False
    else:
        print("\n✅ Tất cả dependencies đã sẵn sàng!")
        return True


def main():
    """Main function"""
    print("🎯 PDF TO DOCX PIPELINE DEMO")
    print("=" * 40)
    
    # Kiểm tra dependencies trước
    if not check_dependencies():
        return
    
    print()
    
    # Chạy demo
    demo_pipeline()


if __name__ == "__main__":
    main()
