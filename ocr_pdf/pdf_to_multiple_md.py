"""
<PERSON><PERSON><PERSON> để convert PDF thành nhiều file Markdown sử dụng Paddle PP-StructureV3
Mỗi trang PDF sẽ được convert thành 1 file MD riêng biệt
"""

from pathlib import Path
from paddleocr import PPStructureV3
import json
import os
from typing import List, Dict, Any


class PDFToMultipleMarkdown:
    def __init__(self, output_base_dir: str = "./output"):
        """
        Khởi tạo converter
        
        Args:
            output_base_dir: Th<PERSON> mục gốc để lưu output
        """
        self.output_base_dir = Path(output_base_dir)
        self.pipeline = PPStructureV3()
        
    def convert_pdf_to_multiple_md(self, input_pdf_path: str) -> Dict[str, Any]:
        """
        Convert PDF thành nhiều file Markdown
        
        Args:
            input_pdf_path: Đường dẫn đến file PDF input
            
        Returns:
            Dict chứa thông tin về các file đã tạo
        """
        input_path = Path(input_pdf_path)
        if not input_path.exists():
            raise FileNotFoundError(f"File PDF không tồn tại: {input_pdf_path}")
            
        # Tạo thư mục output cho file này
        pdf_name = input_path.stem
        output_dir = self.output_base_dir / pdf_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"Đang xử lý PDF: {input_pdf_path}")
        print(f"Output directory: {output_dir}")
        
        # Chạy PP-StructureV3 để extract
        output = self.pipeline.predict(input=str(input_pdf_path))
        
        markdown_files = []
        image_files = []
        
        # Xử lý từng trang
        for page_idx, res in enumerate(output):
            page_num = page_idx + 1
            
            # Lấy markdown content cho trang này
            md_info = res.markdown
            
            if md_info:
                # Tạo file markdown cho trang này
                md_filename = f"page_{page_num:03d}.md"
                md_file_path = output_dir / md_filename
                
                # Lưu markdown content
                with open(md_file_path, "w", encoding="utf-8") as f:
                    f.write(md_info.get("markdown_text", ""))
                
                markdown_files.append({
                    "page": page_num,
                    "filename": md_filename,
                    "path": str(md_file_path),
                    "content_length": len(md_info.get("markdown_text", ""))
                })
                
                print(f"Đã tạo: {md_filename}")
                
                # Xử lý images nếu có
                markdown_images = md_info.get("markdown_images", {})
                if markdown_images:
                    for img_path, image in markdown_images.items():
                        # Tạo đường dẫn image cho trang này
                        img_filename = f"page_{page_num:03d}_{Path(img_path).name}"
                        img_file_path = output_dir / "images" / img_filename
                        img_file_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Lưu image
                        image.save(img_file_path)
                        
                        image_files.append({
                            "page": page_num,
                            "original_path": img_path,
                            "filename": img_filename,
                            "path": str(img_file_path)
                        })
                        
                        print(f"Đã lưu image: {img_filename}")
        
        # Tạo file metadata
        metadata = {
            "source_pdf": str(input_path),
            "output_directory": str(output_dir),
            "total_pages": len(output),
            "markdown_files": markdown_files,
            "image_files": image_files,
            "created_at": str(Path().cwd())
        }
        
        metadata_file = output_dir / "metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
            
        print(f"Đã tạo metadata: {metadata_file}")
        print(f"Tổng cộng: {len(markdown_files)} file MD, {len(image_files)} images")
        
        return metadata
    
    def create_index_file(self, metadata: Dict[str, Any]) -> str:
        """
        Tạo file index.md liệt kê tất cả các file đã tạo
        
        Args:
            metadata: Metadata từ quá trình convert
            
        Returns:
            Đường dẫn đến file index
        """
        output_dir = Path(metadata["output_directory"])
        index_file = output_dir / "index.md"
        
        content = f"""# Index - {Path(metadata['source_pdf']).name}

## Thông tin chung
- **File gốc**: {metadata['source_pdf']}
- **Tổng số trang**: {metadata['total_pages']}
- **Số file Markdown**: {len(metadata['markdown_files'])}
- **Số file hình ảnh**: {len(metadata['image_files'])}

## Danh sách file Markdown

"""
        
        for md_file in metadata['markdown_files']:
            content += f"- [Trang {md_file['page']}]({md_file['filename']}) ({md_file['content_length']} ký tự)\n"
            
        if metadata['image_files']:
            content += "\n## Danh sách hình ảnh\n\n"
            for img_file in metadata['image_files']:
                content += f"- Trang {img_file['page']}: [{img_file['filename']}]({img_file['path']})\n"
        
        with open(index_file, "w", encoding="utf-8") as f:
            f.write(content)
            
        print(f"Đã tạo file index: {index_file}")
        return str(index_file)


def main():
    """Hàm main để test"""
    converter = PDFToMultipleMarkdown("./output_md")
    
    # Sử dụng file PDF mẫu có sẵn
    pdf_files = ["sample.pdf", "test_file.pdf"]
    
    for pdf_file in pdf_files:
        if Path(pdf_file).exists():
            print(f"\n{'='*50}")
            print(f"Xử lý file: {pdf_file}")
            print(f"{'='*50}")
            
            try:
                metadata = converter.convert_pdf_to_multiple_md(pdf_file)
                converter.create_index_file(metadata)
                print(f"✅ Hoàn thành xử lý {pdf_file}")
            except Exception as e:
                print(f"❌ Lỗi khi xử lý {pdf_file}: {e}")
        else:
            print(f"⚠️  File không tồn tại: {pdf_file}")


if __name__ == "__main__":
    main()
