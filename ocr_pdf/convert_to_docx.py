
from pdf_craft import create_pdf_page_extractor, PDFPageExtractor
from pdf_craft import LLM, analyse, generate_epub_file
from pathlib import Path
import os

def pdf_to_epub(input_pdf_path: str, output_epub_path: str):
    """
    Convert PDF sang EPUB bằng pdf_craft.
    """
    # Tạo thư mục tạm thời
    analysing_dir = "tmp_analysis"
    output_dir = "tmp_output"

    # Tạo tất cả các thư mục cần thiết
    Path(analysing_dir).mkdir(parents=True, exist_ok=True)
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    print(f"Created directories: {analysing_dir}, {output_dir}")

    # Kiểm tra file PDF có tồn tại không
    if not Path(input_pdf_path).exists():
        print(f"Error: PDF file '{input_pdf_path}' not found!")
        return

    # Đọc LLM key từ environment variable
    llm_key = os.getenv("LLM_KEY", "sk-35e86951120448a38e6ec44e911a10a8")
    if not llm_key:
        print("Error: LLM_KEY environment variable not set!")
        return

    # Tạo extractor OCR + layout phân tích
    extractor: PDFPageExtractor = create_pdf_page_extractor(
        device="cuda",
        model_dir_path="models",
    )

    # Cấu hình LLM
    llm = LLM(
        key=llm_key,
        url="https://api.deepseek.com",
        model="deepseek-chat",
        token_encoding="o200k_base",
    )

    # Bắt đầu phân tích PDF
    analyse(
        llm=llm,
        pdf_page_extractor=extractor,
        pdf_path=input_pdf_path,
        analysing_dir_path=analysing_dir,
        output_dir_path=output_dir,
    )

    # Sau khi phân tích xong, tạo file EPUB
    generate_epub_file(
        from_dir_path=output_dir,
        epub_file_path=output_epub_path
    )

    print(f"Successfully converted {input_pdf_path} to {output_epub_path}")

if __name__ == "__main__":
    input_pdf = "sample.pdf"
    output_epub = "sample.epub"

    pdf_to_epub(input_pdf, output_epub)
